<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对话框尺寸测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-info {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-btn {
            background: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        /* 修复后的AI训练对话框样式 */
        .ai-training-dialog {
            display: none; /* 默认隐藏 */
            position: fixed;
            z-index: 10001;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

            /* 【关键修复】设置固定的初始尺寸 */
            width: 1188px;
            height: 700px; /* 【重要】设置固定高度 */
            max-width: 95vw;
            max-height: 95vh;

            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);

            flex-direction: column;
            resize: none;
            overflow: hidden; /* 【重要】改为hidden */
            min-width: 600px;
            min-height: 700px; /* 【重要】设置与height相同的最小高度 */
            backdrop-filter: blur(20px);

            font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        /* 显示状态时的样式 */
        .ai-training-dialog[style*="display: flex"] {
            display: flex !important;
        }
        
        .ai-training-dialog-header {
            padding: 20px 25px;
            background: rgba(255, 255, 255, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            flex-shrink: 0;
            min-height: 60px;
        }
        
        .ai-training-dialog-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            height: calc(100% - 80px); /* 【关键修复】减去header高度 */
            min-height: 600px; /* 【重要】设置足够的最小高度 */
        }

        .ai-training-messages {
            flex: 1; /* 【关键修复】改为flex: 1 */
            overflow-y: auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            position: relative;
            backdrop-filter: blur(20px);
            margin: 0;
            height: calc(100% - 120px); /* 减去输入区域高度 */
            min-height: 400px;
        }
        
        .ai-training-input-area {
            padding: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            position: relative;
            flex-shrink: 0;
            height: 120px; /* 【关键修复】设置固定高度 */
            box-sizing: border-box; /* 【重要】确保padding包含在高度内 */
        }
        
        .ai-training-input-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .ai-training-input-area textarea {
            flex-grow: 1;
            padding: 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 25px;
            resize: none;
            font-size: 14px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            font-family: inherit;
            line-height: 1.4;
            min-width: 0;
        }
        
        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .size-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10002;
            display: none;
        }
        
        .ai-training-msg {
            display: flex;
            margin-bottom: 20px;
            max-width: 75%;
            align-items: flex-end;
        }
        
        .ai-training-msg .avatar {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            text-align: center;
            line-height: 42px;
            font-weight: 600;
            flex-shrink: 0;
            color: white;
            font-size: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 5px;
        }
        
        .ai-training-msg .content {
            margin: 0 15px;
            padding: 15px 20px;
            border-radius: 18px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            line-height: 1.5;
            border: 1px solid rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>AI训练对话框尺寸修复测试</h1>
        <p><strong>期望：打开时立即显示为1188px × 700px的固定尺寸</strong></p>
        <p style="font-size: 14px; opacity: 0.9;">修复要点：设置固定高度，确保不依赖内容撑开</p>
        <div style="margin: 20px 0;">
            <button class="test-btn" onclick="showDialog()">显示对话框</button>
            <button class="test-btn" onclick="hideDialog()">隐藏对话框</button>
            <button class="test-btn" onclick="toggleSizeInfo()">显示/隐藏尺寸信息</button>
        </div>
    </div>

    <div class="size-info" id="sizeInfo">
        <div>对话框尺寸信息：</div>
        <div id="sizeDetails">未显示</div>
    </div>

    <div class="ai-training-dialog" id="aiTrainingDialog">
        <div class="ai-training-dialog-header">
            <span>AI 智能体训练 - 尺寸测试</span>
            <button class="close-btn" onclick="hideDialog()">&times;</button>
        </div>
        <div class="ai-training-dialog-body">
            <div class="ai-training-messages" id="aiTrainingMessages">
                <div class="ai-training-msg">
                    <div class="avatar">AI</div>
                    <div class="content">这是一个测试消息。对话框应该在打开时就显示为1188px宽度，而不需要等待输入内容。</div>
                </div>
            </div>
            <div class="ai-training-input-area">
                <div class="ai-training-input-container">
                    <textarea placeholder="测试输入框..."></textarea>
                </div>
            </div>
        </div>
    </div>

    <script>
        let sizeInfoVisible = false;
        let sizeUpdateInterval = null;
        
        function showDialog() {
            const dialog = document.getElementById('aiTrainingDialog');
            dialog.style.display = 'flex';

            // 立即检查尺寸
            setTimeout(() => {
                updateSizeInfo();
                const rect = dialog.getBoundingClientRect();
                console.log('对话框已显示，当前尺寸：', rect);

                // 验证尺寸是否正确
                const widthCorrect = Math.abs(rect.width - 1188) < 10;
                const heightCorrect = Math.abs(rect.height - 700) < 10;

                if (widthCorrect && heightCorrect) {
                    console.log('✅ 尺寸修复成功！对话框立即显示为期望大小');
                } else {
                    console.log('❌ 尺寸仍有问题：', {
                        expectedWidth: 1188,
                        actualWidth: rect.width,
                        expectedHeight: 700,
                        actualHeight: rect.height
                    });
                }
            }, 100);
        }
        
        function hideDialog() {
            const dialog = document.getElementById('aiTrainingDialog');
            dialog.style.display = 'none';
            
            if (sizeUpdateInterval) {
                clearInterval(sizeUpdateInterval);
                sizeUpdateInterval = null;
            }
        }
        
        function toggleSizeInfo() {
            const sizeInfo = document.getElementById('sizeInfo');
            sizeInfoVisible = !sizeInfoVisible;
            sizeInfo.style.display = sizeInfoVisible ? 'block' : 'none';
            
            if (sizeInfoVisible) {
                updateSizeInfo();
                sizeUpdateInterval = setInterval(updateSizeInfo, 500);
            } else if (sizeUpdateInterval) {
                clearInterval(sizeUpdateInterval);
                sizeUpdateInterval = null;
            }
        }
        
        function updateSizeInfo() {
            const dialog = document.getElementById('aiTrainingDialog');
            const sizeDetails = document.getElementById('sizeDetails');
            
            if (dialog.style.display === 'flex') {
                const rect = dialog.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(dialog);
                
                sizeDetails.innerHTML = `
                    实际宽度: ${Math.round(rect.width)}px<br>
                    实际高度: ${Math.round(rect.height)}px<br>
                    CSS宽度: ${computedStyle.width}<br>
                    CSS高度: ${computedStyle.height}<br>
                    显示状态: ${dialog.style.display}<br>
                    视窗宽度: ${window.innerWidth}px<br>
                    期望尺寸: 1188px × 700px<br>
                    宽度匹配: ${Math.abs(rect.width - 1188) < 10 ? '✅' : '❌'}<br>
                    高度匹配: ${Math.abs(rect.height - 700) < 10 ? '✅' : '❌'}
                `;
            } else {
                sizeDetails.innerHTML = '对话框未显示';
            }
        }
        
        // 页面加载完成后自动显示对话框
        window.addEventListener('load', () => {
            setTimeout(() => {
                showDialog();
                toggleSizeInfo();
            }, 500);
        });
    </script>
</body>
</html>
