<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI训练对话框测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-controls {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        /* 复制AI训练对话框的样式 */
        .ai-training-dialog {
            display: none;
            position: fixed;
            z-index: 10001;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            
            /* 与控制面板保持一致的A4纸张尺寸 */
            width: 1188px;
            max-width: 95vw;
            max-height: 95vh;
            
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);
            display: flex;
            flex-direction: column;
            resize: none;
            overflow-y: auto;
            overflow-x: visible;
            min-width: 600px;
            min-height: 400px;
            backdrop-filter: blur(20px);
            
            font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .ai-training-dialog-header {
            padding: 20px 25px;
            cursor: move;
            z-index: 10;
            background: rgba(255, 255, 255, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            flex-shrink: 0;
            min-height: 60px;
        }
        
        .ai-training-dialog-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            min-height: 500px; /* 设置最小高度，确保对话框打开时有合适的大小 */
        }

        .ai-training-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            position: relative;
            backdrop-filter: blur(20px);
            margin: 0;
            min-height: 350px; /* 设置最小高度，确保消息区域有足够的显示空间 */
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }
        
        .ai-training-messages::-webkit-scrollbar {
            width: 8px;
        }
        
        .ai-training-messages::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .ai-training-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }
        
        .ai-training-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }
        
        .ai-training-msg {
            display: flex;
            margin-bottom: 20px;
            max-width: 75%;
            animation: fadeInUp 0.3s ease;
            align-items: flex-end;
        }
        
        .ai-training-msg.user {
            margin-left: auto;
            flex-direction: row-reverse;
        }
        
        .ai-training-msg .avatar {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            line-height: 42px;
            font-weight: 600;
            flex-shrink: 0;
            color: white;
            font-size: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 5px;
        }
        
        .ai-training-msg.user .avatar {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
        }
        
        .ai-training-msg.bot .avatar {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .ai-training-msg .content {
            margin: 0 15px;
            padding: 15px 20px;
            border-radius: 18px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            line-height: 1.5;
        }
        
        .ai-training-msg.user .content {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
            color: white;
        }
        
        .ai-training-msg.bot .content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .ai-training-input-area {
            padding: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            position: relative;
            flex-shrink: 0;
        }
        
        .ai-training-input-container {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .ai-training-input-area textarea {
            flex-grow: 1;
            padding: 15px 60px 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 25px;
            resize: none;
            font-size: 14px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.4;
            min-width: 0;
            max-height: 120px;
        }
        
        .close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 响应式设计 */
        @media (max-width: 1300px) {
            .ai-training-dialog {
                width: 95vw;
                max-width: 95vw;
                margin: 0 2.5vw;
            }
            
            .ai-training-messages {
                padding: 20px;
            }
            
            .ai-training-input-area {
                padding: 20px;
            }
            
            .ai-training-msg {
                max-width: 85%;
            }
        }
        
        @media (max-width: 768px) {
            .ai-training-dialog {
                width: 98vw;
                max-width: 98vw;
                margin: 0 1vw;
                border-radius: 12px;
            }
            
            .ai-training-dialog-header {
                padding: 15px 20px;
                font-size: 16px;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }
            
            .ai-training-messages {
                padding: 15px;
            }
            
            .ai-training-input-area {
                padding: 15px;
            }
            
            .ai-training-msg {
                max-width: 90%;
            }
            
            .ai-training-msg .content {
                padding: 12px 16px;
                font-size: 13px;
            }
            
            .ai-training-input-area textarea {
                height: 45px;
                font-size: 13px;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h1 style="color: white; text-align: center;">AI训练对话框优化测试</h1>
        <p style="color: white; text-align: center; margin-bottom: 20px;">
            期望尺寸：1188px × 自适应高度（最小500px）
        </p>
        <button class="test-btn" onclick="showDialog()">显示对话框</button>
        <button class="test-btn" onclick="addSampleMessages()">添加示例消息</button>
        <button class="test-btn" onclick="clearMessages()">清空消息</button>
        <button class="test-btn" onclick="checkDialogSize()">检查对话框尺寸</button>
    </div>

    <div class="ai-training-dialog" id="aiTrainingDialog">
        <div class="ai-training-dialog-header">
            <span>AI 智能体训练 - 横向A4纸尺寸优化</span>
            <button class="close-btn" onclick="hideDialog()">&times;</button>
        </div>
        <div class="ai-training-dialog-body">
            <div class="ai-training-messages" id="aiTrainingMessages">
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">欢迎使用AI智能体训练功能！现在对话框已经优化为横向A4纸尺寸（1188px宽度），与控制面板保持一致。</div>
                </div>
            </div>
            <div class="ai-training-input-area">
                <div class="ai-training-input-container">
                    <textarea placeholder="在此输入内容进行训练..."></textarea>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDialog() {
            document.getElementById('aiTrainingDialog').style.display = 'flex';
        }
        
        function hideDialog() {
            document.getElementById('aiTrainingDialog').style.display = 'none';
        }
        
        function addSampleMessages() {
            const messagesContainer = document.getElementById('aiTrainingMessages');
            const sampleMessages = [
                { type: 'user', content: '请帮我优化客服回复模板' },
                { type: 'bot', content: '好的！我可以帮您优化客服回复模板。请告诉我您希望优化哪个方面：\n\n1. 回复速度\n2. 回复质量\n3. 个性化程度\n4. 专业性' },
                { type: 'user', content: '我希望提高回复的个性化程度，让客户感受到更贴心的服务' },
                { type: 'bot', content: '很好的想法！个性化回复确实能提升客户体验。我建议从以下几个方面入手：\n\n• 使用客户的姓名\n• 根据购买历史推荐相关产品\n• 记住客户的偏好和特殊需求\n• 使用温暖友好的语调\n• 提供个性化的解决方案\n\n您希望我帮您制作一些个性化的回复模板吗？' }
            ];
            
            sampleMessages.forEach(msg => {
                const msgElement = document.createElement('div');
                msgElement.className = `ai-training-msg ${msg.type}`;
                msgElement.innerHTML = `
                    <div class="avatar">${msg.type === 'user' ? '用户' : 'AI'}</div>
                    <div class="content">${msg.content}</div>
                `;
                messagesContainer.appendChild(msgElement);
            });
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function clearMessages() {
            const messagesContainer = document.getElementById('aiTrainingMessages');
            messagesContainer.innerHTML = `
                <div class="ai-training-msg bot">
                    <div class="avatar">AI</div>
                    <div class="content">欢迎使用AI智能体训练功能！现在对话框已经优化为横向A4纸尺寸（1188px宽度），与控制面板保持一致。</div>
                </div>
            `;
        }

        function checkDialogSize() {
            const dialog = document.getElementById('aiTrainingDialog');
            if (dialog.style.display === 'flex') {
                const rect = dialog.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(dialog);
                alert(`对话框当前尺寸：
宽度: ${rect.width}px (期望: 1188px)
高度: ${rect.height}px (最小期望: 500px)
显示状态: ${dialog.style.display}
CSS宽度: ${computedStyle.width}
CSS高度: ${computedStyle.height}`);
            } else {
                alert('对话框当前未显示，请先点击"显示对话框"按钮');
            }
        }

        // 自动显示对话框
        setTimeout(showDialog, 500);
    </script>
</body>
</html>
